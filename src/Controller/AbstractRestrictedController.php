<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Controller\AbstractView\GenericView;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\UserRedirect;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Abstract Restricted Controller
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR> <PERSON>
 * Migrated to Symfony by Augment Agent
 */
abstract class AbstractRestrictedController extends AbstractCommonController
{
    /**
     * @var \Zend_Session_Namespace
     */
    private $_session;

    private $_endDate;
    private $_beginDate;
    private $_trueBeginDate;
    private $_trueEndDate;
    private $_loggedUser = false;

    // Tab constants
    const TAB_HOME          = 'home';
    const TAB_BILLING       = 'billing';
    const TAB_FACILITY      = 'facilities';
    const TAB_SETTINGS      = 'settings';
    const TAB_WIDGET        = 'widget';
    const TAB_HOSTEDWEBSITE = 'hostedwebsite';
    const TAB_MOVE_INS      = 'move-ins';

    protected $view;

    /**
     * Migrated from final public function init()
     * 
     */
    public function init(Request $request): mixed
    {
        $this->initBeforeControllerAction($request);
        $this->setRequest($request);
        // we can set View in initBeforeControllerAction
        if (empty($this->view)) {
            $this->view = new GenericView();
        }

        // Initialize authorization - this may return a redirect response
        $authResponse = $this->_initAuthorization($request);
        if ($authResponse) {
            return $authResponse;
        }

        // Call child controller initialization
        $this->_init($request);

        // Initialize date ranges and UI state
        $this->_initDateRange($request);
        $this->_initTrueDateRange($request);
        $this->_initUiState($request);

        $this->view->errorMessages = [];
        $this->view->successMessages = [];

        return null;
    }

    private function _clear(): void
    {
        $this->_loggedUser = null;
        $this->_session = null;
        $this->_beginDate = null;
        $this->_endDate = null;
        $this->_trueBeginDate = null;
        $this->_trueEndDate = null;
    }

    private function _forceTerms(Request $request): ?Response
    {
        // Rules only apply to these lesser account roles
        if ($this->getLoggedUser()->isMyFootGod()) {
            return null;
        }

        // Get controller name from route
        $route = $request->attributes->get('_route');
        $controllerName = $this->getControllerNameFromRoute($route);

        // Certain urls can work without terms, let the accounts controller pass
        // or else they can never add payment methods and pass this point
        if ($controllerName === 'accounts') {
            return null;
        }
        // Let the error controller fire
        if ($controllerName === 'error') {
            return null;
        }

        // Force agree to the pre-terms
        if (! \Genesis_Service_Account::termsAccepted($this->getLoggedUser()->getAccount())) {
            if (! $this->getLoggedUser()->isMyfootAdmin()) {
                return null; // tech debt. Fix this
                $message = '(terms incomplete) Please ask your account admin to sign in. '
                    .' Your account admins are: ';
                /**
                 * @var $adminUser \Genesis_Entity_User
                 */
                foreach ($this->getLoggedUser()->getAccount()->getAdmins() as $adminUser) {
                    $message .= $adminUser->getEmail() . " ";
                }

                throw new \Exception($message);
            }
            if ($controllerName !== 'signup-end' &&
                $request->get('action') !== 'terms'
            ) {
                return $this->redirect('/signup-end/terms');
            }
        }

        // Encourage to setup a billable entity
        if ($this->getLoggedUser()->getAccount()->getNumBillableEntities() === 0) {
            if ($this->getLoggedUser()->isMyfootAdmin() &&
                $controllerName === 'login' &&
                $request->get('action') === 'process-login') {
                    return $this->redirect('/signup-end/billing');
            }
        }

        return null;
    }

    private function _initAuthorization(Request $request): ?Response
    {
        // The user is not signed in. make them go sign in
        if (! $this->getLoggedUser() instanceof \Genesis_Entity_UserAccess) {
            /* Grab the URL and jam into a cookie so we can redirect on login */
            // Only set this if the URL is a GET, otherwise we might end up somewhere
            // and we won't have the parameters in the original POST, causing errors
            // (See mantis 1769)
            UserRedirect::setRedirect($request->getRequestUri());
            return $this->redirectToRoute('login_index');
        }

        $forceTermsResponse = $this->_forceTerms($request);
        if ($forceTermsResponse) {
            return $forceTermsResponse;
        }

        if ($this->getLoggedUser()->isMyFootGod()) {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }

        $genesisRequest = new \Genesis_Util_Request();
        \Genesis_Service_ActivityLog::logMyFootPageview($genesisRequest, $this->getLoggedUser());

        // Forward them to signup if they either do not have an account or do not have facilities yet
        if ($this->getLoggedUser() && (!$this->getLoggedUser()->getAccountId() || ! $this->getLoggedUser()->getMyfootRole())) {
            $controllerName = $this->getControllerNameFromRoute($request->attributes->get('_route'));
            if ($controllerName !== "signup") {
                if ($this->getLoggedUser()->getAccount() && $this->getLoggedUser()->getAccount()->getNumFacilities() == 0) {
                    return $this->redirect($this->generateUrl('features_type'));
                } else if ($controllerName !== "booking") {
                    return $this->redirect('/signup-start');
                }
            }
        }

        // We have an account ID now, but we might not have a valid Facility
        $this->view->accountId = $this->getSession()->accountId;
        $this->view->facilityId = $this->getSession()->facilityId; // used by the menu

        // The controller names a facility editor should have access to
        $this->view->sitesControllers = ['sites', 'settings'];

        // Set these so JS can also access
        $authBearerToken = UserOauth::getToken();
        if ($authBearerToken) {
            // Unfortunately, we have to reference 'SF_ENV' here since servicesBaseUrl is used many times throughout client-side
            // JS. Also, we can't change getDomain() inside authorization service's code to locally use the FQDN instead of the
            // docker link. This is because we can't reference the FQDN for local authorization service inside MyFoot's container.
            $this->view->servicesBaseUrl = getenv('SERVICES_BASE_URL');
            $this->view->authBearerToken = $authBearerToken->__toString();
        }

        return null;
    }

    private function _initDateRange(Request $request): void
    {
        $dateRange = $request->get('date_range');
        if ($dateRange) {
            $this->getSession()->dateRange = $dateRange;
        } elseif (!$this->getSession()->dateRange) {
            $this->getSession()->dateRange = 'week';
        }

        $this->view->dateRange = $this->getSession()->dateRange;

        $this->_endDate = date('Y-m-d H:i:s');

        switch ($this->getSession()->dateRange) {
            case 'week':
                $this->_beginDate = date('Y-m-d', strtotime('-1 week'));
                break;
            case 'month':
                $this->_beginDate = date('Y-m-d', strtotime('-1 month'));
                break;
            case 'year':
                $this->_beginDate = date('Y-m-d', strtotime('-1 year'));
                break;
        }
    }

    private function _initTrueDateRange(Request $request): void
    {
        $trueDateRange = $request->get('true_date_range');
        if ($trueDateRange) {
            $this->getSession()->trueDateRange = $trueDateRange;
        } else {
            $this->getSession()->trueDateRange = date('M j, Y', strtotime('-1 month')) . ' - ' . date('M j, Y');
        }

        $this->view->trueDateRange = $this->getSession()->trueDateRange;
        $dates = explode(' - ', $this->view->trueDateRange);

        if (count($dates) == 2) {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $dates[1] . " 23:59:59";
        } else {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $this->_trueBeginDate . " 23:59:59";
        }

        $this->view->trueBeginDate = $this->_trueBeginDate;
        $this->view->trueEndDate = $this->_trueEndDate;
    }

    private function _initUiState(Request $request): void
    {
        $this->view->selectedTab = $this->getTab();

        if ($request->get('welcome')) {
            $this->view->welcomeMessage = true;
        }
    }

    /**
     * Initialize controller - override in child controllers
     * @param Request $request The request object (available for child controllers)
     */
    protected function _init(Request $request): void
    {
        // Override in child controllers
        // Note: $request parameter is available for child controllers to use
        unset($request); // Suppress unused parameter warning
    }

    /**
     * @return \Genesis_Entity_UserAccess|false
     */
    protected function getLoggedUser()
    {
        if (! $this->_loggedUser) {
            // The account id the user posed should be in session by now, if they are a god this will modify
            // user access and allow it
            try {
                $this->_loggedUser = User::getLoggedUser();
            } catch (\Exception $e) {
                // Logout user if there is no auth bearer token in cookie
                $message = 'Auth token is invalid or has expired.';
                if (! \Genesis_Config_Server::isProduction()) {
                    $message .= "<br/>Exception: " . $e->getMessage();
                }
                $this->getSession()->logoutMessage = $message;
                return $this->redirectToRoute('login_loign_logout');
            }
        }

        return $this->view->loggedUser = $this->_loggedUser;
    }

    /**
     * @return \Zend_Session_Namespace
     */
    protected function getSession()
    {
        if (! $this->_session) {
            $this->view->session = $this->_session = User::getSession();
        }

        return $this->_session;
    }

    protected function getBeginDate(): ?string
    {
        return $this->_beginDate;
    }

    protected function getEndDate(): ?string
    {
        return $this->_endDate;
    }

    protected function getTrueBeginDate(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->_trueBeginDate));
    }

    protected function getTrueEndDate(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->_trueEndDate));
    }

    protected function getSideBarContent(): string
    {
        return '';
    }

    protected function getTab(): string
    {
        return '';
    }

    protected function dispatchError($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->errorMessages)) {
            $this->view->errorMessages[] = $messages;
        } else {
            $this->view->errorMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('error', $messages);
    }

    protected function dispatchSuccess($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->successMessages)) {
            $this->view->successMessages[] = $messages;
        } else {
            $this->view->successMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('success', $messages);
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     * In Symfony, this is typically handled by the Request object and form validation
     *
     * @param Request $request
     * @param string $paramName
     * @param mixed $default
     * @return mixed
     */
    public function getParam(Request $request, string $paramName, $default = null)
    {
        $param = $request->get($paramName, $default);
        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    public function __destruct()
    {
        $this->_clear();
    }
}